name: thingsboard_app
description: Flutter ThingsBoard Mobile Application

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.6.0

environment:
  sdk: ">=3.2.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  thingsboard_client: ^4.0.0
  intl: ^0.19.0
  flutter_secure_storage: ^9.0.0
  flutter_speed_dial: ^7.0.0
  cupertino_icons: ^1.0.6
  fluro: ^2.0.5
  flutter_svg: ^2.0.9
  jovial_svg: ^1.1.19
  auto_size_text: ^3.0.0-nullsafety.0
  infinite_scroll_pagination: ^4.0.0
  fading_edge_scrollview: ^4.0.0
  stream_transform: ^2.1.0
  flutter_inappwebview: ^6.1.5
  url_launcher: ^6.2.1
  image_picker: ^1.0.4
  mime: ^1.0.4
  logger: ^2.5.0
  qr_code_scanner: ^1.0.1
  device_info_plus: ^10.1.0
  geolocator: ^12.0.0
  geolocator_android: 4.6.1
  material_design_icons_flutter: ^7.0.7296
  package_info_plus: ^8.0.0
  dart_jsonwebtoken: ^2.12.1
  crypto: ^3.0.3
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^10.0.1
  flutter_html: 3.0.0-beta.2
  universal_html: ^2.2.4
  universal_platform: ^1.0.0+1
  preload_page_view: ^0.2.0
  flutter_localizations:
    sdk: flutter
  firebase_core: ^3.1.0
  firebase_messaging: ^15.0.1
  flutter_local_notifications: ^17.1.2
  flutter_new_badger: ^1.0.1
  timeago: ^3.6.1
  flutter_slidable:
    git:
      url: https://github.com/letsar/flutter_slidable.git
      ref: master
  flutter_bloc: ^8.1.5
  get_it: ^7.6.7
  equatable: ^2.0.5
  app_links: ^6.3.2
  collection: ^1.18.0
  html: ^0.15.4
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  modal_bottom_sheet: ^3.0.0
  event_bus: ^2.0.0
  google_fonts: ^6.2.1
  expandable: ^5.0.1
  focused_menu: ^1.0.5
  flutter_esp_ble_prov: ^0.1.7
  #  esp_smartconfig: ^2.0.4
  esp_provisioning_softap: ^1.0.3
  permission_handler: ^11.3.1
  wifi_scan: ^0.4.1+1
  open_settings_plus: ^0.4.0
  plugin_wifi_connect:
    git:
      url: https://github.com/thingsboard/flutter_wifi_connect
  toastification: ^2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  mocktail: ^1.0.3
  bloc_test: ^9.1.7
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.9

dependency_overrides:
  logger: ^2.5.0
  http: ^1.3.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
  generate: true

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/thingsboard.png"
flutter_intl:
  enabled: true
