PODS:
  - app_links (0.0.2):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - esp_provisioning_softap (0.0.1):
    - Flutter
  - ESPProvision (3.0.2):
    - ESPProvision/Core (= 3.0.2)
  - ESPProvision/Core (3.0.2):
    - SwiftProtobuf (~> 1.22.0)
  - Firebase/CoreOnly (11.2.0):
    - FirebaseCore (= 11.2.0)
  - Firebase/Messaging (11.2.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.2.0)
  - firebase_core (3.6.0):
    - Firebase/CoreOnly (= 11.2.0)
    - Flutter
  - firebase_messaging (15.1.3):
    - Firebase/Messaging (= 11.2.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.2.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.4.2):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.2.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_esp_ble_prov (0.0.1):
    - ESPProvision
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_new_badger (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_settings_plus (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - plugin_wifi_connect (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - SwiftProtobuf (1.22.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - wifi_scan (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - esp_provisioning_softap (from `.symlinks/plugins/esp_provisioning_softap/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_esp_ble_prov (from `.symlinks/plugins/flutter_esp_ble_prov/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_new_badger (from `.symlinks/plugins/flutter_new_badger/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_settings_plus (from `.symlinks/plugins/open_settings_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - plugin_wifi_connect (from `.symlinks/plugins/plugin_wifi_connect/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wifi_scan (from `.symlinks/plugins/wifi_scan/ios`)

SPEC REPOS:
  trunk:
    - ESPProvision
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SwiftProtobuf

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  esp_provisioning_softap:
    :path: ".symlinks/plugins/esp_provisioning_softap/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_esp_ble_prov:
    :path: ".symlinks/plugins/flutter_esp_ble_prov/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_new_badger:
    :path: ".symlinks/plugins/flutter_new_badger/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_settings_plus:
    :path: ".symlinks/plugins/open_settings_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  plugin_wifi_connect:
    :path: ".symlinks/plugins/plugin_wifi_connect/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wifi_scan:
    :path: ".symlinks/plugins/wifi_scan/ios"

SPEC CHECKSUMS:
  app_links: 3da4c36b46cac3bf24eb897f1a6ce80bda109874
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  esp_provisioning_softap: afae2212ada1e46857f83a6c522a3f848f56bd0e
  ESPProvision: ffc50093a0a3e72a80d03116bbe0d0d4bb4252b7
  Firebase: 98e6bf5278170668a7983e12971a66b2cd57fc8c
  firebase_core: 085320ddfaacb80d1a96eac3a87857afcc150db1
  firebase_messaging: d398edc15fe825f832836e74f6ac61e8cd2f3ad3
  FirebaseCore: a282032ae9295c795714ded2ec9c522fc237f8da
  FirebaseCoreInternal: 35731192cab10797b88411be84940d2beb33a238
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: c9ec7b90c399c7a6100297e9d16f8a27fc7f7152
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_esp_ble_prov: 6cff8544535225431d58e5577706ba2a1a106303
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_new_badger: 133aaf93e9a5542bf905c8483d8b83c5ef4946ea
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  geolocator_apple: d981750b9f47dbdb02427e1476d9a04397beb8d9
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_settings_plus: d19f91e8a04649358a51c19b484ce2e637149d70
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  plugin_wifi_connect: c3ec7b1449a225b34cd0e7e0e9b1939d3234514d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  SwiftProtobuf: 7773c4e96a99d7b8ab7cda0fc30a883732ff93b1
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wifi_scan: 3254c160abc7266feac359a927f8ab64f2703774

PODFILE CHECKSUM: 247417e21c6e70edbee75b4c5f4642a102ec67a9

COCOAPODS: 1.16.2
